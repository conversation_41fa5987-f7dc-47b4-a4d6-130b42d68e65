import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { DashboardTest } from '../src/components/dashboard/dashboard-test'

describe('Dashboard Components', () => {
  it('should render dashboard test component', () => {
    render(<DashboardTest />)
    
    expect(screen.getByText('Dashboard Components Test')).toBeInTheDocument()
    expect(screen.getByText('✅ Dashboard Components Created:')).toBeInTheDocument()
    expect(screen.getByText('🔐 Test Users Available:')).toBeInTheDocument()
    expect(screen.getByText('📱 Sidebar Features:')).toBeInTheDocument()
  })

  it('should show test users information', () => {
    render(<DashboardTest />)
    
    expect(screen.getByText('<EMAIL> (Student role)')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL> (Teacher role)')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL> (Admin role)')).toBeInTheDocument()
  })

  it('should show sidebar features', () => {
    render(<DashboardTest />)
    
    expect(screen.getByText('• Current Entries - View active submissions')).toBeInTheDocument()
    expect(screen.getByText('• Past Entries - View submission history')).toBeInTheDocument()
    expect(screen.getByText('• New Entry - Create new submission')).toBeInTheDocument()
    expect(screen.getByText('• Competition Results - View results')).toBeInTheDocument()
    expect(screen.getByText('• Contact Support - Get help')).toBeInTheDocument()
  })
})
