import { createFileRoute, redirect, useNavigate } from '@tanstack/react-router'
import React, { useState } from 'react'

// Test users for development
const TEST_USERS = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'user'
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'Test Admin',
    role: 'admin'
  }
]

export const Route = createFileRoute('/auth-simple')({
  beforeLoad: ({ context, location }) => {
    // Redirect to dashboard if already authenticated (only check on client side)
    if (typeof window !== 'undefined') {
      const storedUser = localStorage.getItem('fme-user')
      if (storedUser) {
        throw redirect({
          to: '/dashboard',
        })
      }
    }
  },
  component: AuthSimplePage,
})

function AuthSimplePage() {
  const navigate = useNavigate()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Login attempt:', { email, password })
    
    setIsLoading(true)
    setError(null)
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Find test user
      const testUser = TEST_USERS.find(u => u.email === email)
      console.log('Found user:', testUser)

      if (!testUser) {
        console.error('User not found for email:', email)
        throw new Error('Invalid email or password')
      }

      // For demo purposes, any password works for test users
      if (password.length < 3) {
        console.error('Password too short:', password.length)
        throw new Error('Password must be at least 3 characters')
      }

      // Store user (only on client side)
      if (typeof window !== 'undefined') {
        localStorage.setItem('fme-user', JSON.stringify(testUser))
        console.log('User stored in localStorage')
      }

      console.log('Navigating to dashboard...')
      // Redirect to dashboard on successful login
      navigate({ to: '/dashboard' })
    } catch (err) {
      console.error('Login failed:', err)
      setError(err instanceof Error ? err.message : 'Login failed')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-950">
      {/* Header */}
      <header className="bg-brand-nav">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <div className="flex items-center">
              <h1 className="text-white font-bold text-xl">Foundation for Music Education</h1>
            </div>
            <a
              href="/"
              className="text-white hover:text-white/90 text-sm"
            >
              ← Back to Home
            </a>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div className="w-full max-w-md">
          {/* Background Card */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                Welcome Back
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                Sign in to your account (Simple Version)
              </p>
            </div>

            {/* Error Display */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Email Address
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2 transition-colors"
                  placeholder="Enter your email"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Password
                </label>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2 transition-colors"
                  placeholder="Enter your password"
                />
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-brand-button hover:bg-brand-button-hover text-white font-medium py-3 px-4 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    Signing In...
                  </div>
                ) : (
                  'Sign In'
                )}
              </button>
            </form>

            <div className="mt-6 text-center">
              <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <p className="font-medium">Test Users:</p>
                <div className="text-xs space-y-1">
                  <p>User: <EMAIL></p>
                  <p>Admin: <EMAIL></p>
                  <p className="text-muted-foreground">Password: any 3+ characters</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
