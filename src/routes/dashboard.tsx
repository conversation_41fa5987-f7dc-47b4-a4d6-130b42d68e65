import { createFileRoute, redirect } from '@tanstack/react-router'
import { DashboardLayout } from '@/components/dashboard/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { 
  FileText, 
  Clock, 
  Trophy, 
  PlusCircle,
  Calendar,
  Music,
  Star
} from 'lucide-react'

export const Route = createFileRoute('/dashboard')({
  beforeLoad: ({ context, location }) => {
    // In a real app, you'd check authentication here
    // For now, we'll just redirect to auth if no user is stored
    const storedUser = localStorage.getItem('fme-user')
    if (!storedUser) {
      throw redirect({
        to: '/auth',
        search: {
          redirect: location.href,
        },
      })
    }
  },
  component: DashboardPage,
})

function DashboardPage() {
  const { user } = useAuth()

  // Mock data for demonstration
  const currentEntries = [
    {
      id: '1',
      title: 'Piano Solo - Chopin Nocturne',
      category: 'Piano Solo',
      status: 'draft',
      deadline: '2024-03-15',
      lastModified: '2024-01-15'
    },
    {
      id: '2', 
      title: 'String Quartet Performance',
      category: 'Chamber Music',
      status: 'submitted',
      deadline: '2024-03-20',
      lastModified: '2024-01-10'
    }
  ]

  const recentActivity = [
    {
      id: '1',
      action: 'Submitted entry',
      entry: 'String Quartet Performance',
      date: '2024-01-10'
    },
    {
      id: '2',
      action: 'Started new entry',
      entry: 'Piano Solo - Chopin Nocturne', 
      date: '2024-01-08'
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="secondary">Draft</Badge>
      case 'submitted':
        return <Badge variant="default">Submitted</Badge>
      case 'under_review':
        return <Badge variant="outline">Under Review</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <DashboardLayout 
      title={`Welcome back, ${user?.name}`}
      description="Manage your Mark of Excellence competition entries"
    >
      <div className="space-y-6">
        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Entries</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2</div>
              <p className="text-xs text-muted-foreground">
                1 draft, 1 submitted
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Deadlines</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1</div>
              <p className="text-xs text-muted-foreground">
                Due in 8 days
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Past Awards</CardTitle>
              <Trophy className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">
                2 gold, 1 silver
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Competition Year</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2024</div>
              <p className="text-xs text-muted-foreground">
                Registration open
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Current Entries */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Current Entries</CardTitle>
                <CardDescription>
                  Your active competition submissions
                </CardDescription>
              </div>
              <Button size="sm" className="gap-2">
                <PlusCircle className="h-4 w-4" />
                New Entry
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {currentEntries.map((entry) => (
                <div key={entry.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Music className="h-5 w-5 text-primary" />
                    <div>
                      <h4 className="font-medium">{entry.title}</h4>
                      <p className="text-sm text-muted-foreground">{entry.category}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    {getStatusBadge(entry.status)}
                    <div className="text-right text-sm">
                      <p className="text-muted-foreground">Due: {entry.deadline}</p>
                      <p className="text-xs text-muted-foreground">Modified: {entry.lastModified}</p>
                    </div>
                    <Button variant="outline" size="sm">
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Your latest actions and updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  <Star className="h-4 w-4 text-primary" />
                  <div className="flex-1">
                    <p className="text-sm">
                      <span className="font-medium">{activity.action}</span>
                      {' '}
                      <span className="text-muted-foreground">"{activity.entry}"</span>
                    </p>
                    <p className="text-xs text-muted-foreground">{activity.date}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
