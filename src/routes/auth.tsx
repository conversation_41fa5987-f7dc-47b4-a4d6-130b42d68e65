import { createFileRoute, redirect, useNavigate } from '@tanstack/react-router'
import React from 'react'
import { LoginForm } from '@/components/auth/login-form'

// Test users for development (duplicated from auth-context for auth page)
const TEST_USERS = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'Test Student',
    role: 'student'
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'Test Teacher',
    role: 'teacher'
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: 'Test Admin',
    role: 'admin'
  }
]

export const Route = createFileRoute('/auth')({
  beforeLoad: ({ context, location }) => {
    // Redirect to dashboard if already authenticated
    const storedUser = localStorage.getItem('fme-user')
    if (storedUser) {
      throw redirect({
        to: '/dashboard',
      })
    }
  },
  component: AuthPage,
})

function AuthPage() {
  const navigate = useNavigate()

  const handleLogin = async (data: { email: string; password: string }) => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Find test user
    const testUser = TEST_USERS.find(u => u.email === data.email)

    if (!testUser) {
      throw new Error('Invalid email or password')
    }

    // For demo purposes, any password works for test users
    if (data.password.length < 3) {
      throw new Error('Password must be at least 3 characters')
    }

    // Store user
    localStorage.setItem('fme-user', JSON.stringify(testUser))

    // Redirect to dashboard on successful login
    navigate({ to: '/dashboard' })
  }

  const handleSwitchToRegister = () => {
    // For now, just show an alert. In the future, this could navigate to a register page
    alert('Registration feature coming soon!')
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-950">
      {/* Header */}
      <header className="bg-brand-nav">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <div className="flex items-center">
              <h1 className="text-white font-bold text-xl">Foundation for Music Education</h1>
            </div>
            <a
              href="/"
              className="text-white hover:text-white/90 text-sm"
            >
              ← Back to Home
            </a>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div className="w-full max-w-md">
          {/* Background Card */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-8">
            <LoginForm
              onSubmit={handleLogin}
              onSwitchToRegister={handleSwitchToRegister}
            />

            <div className="mt-6 text-center">
              <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <p className="font-medium">Test Users:</p>
                <div className="text-xs space-y-1">
                  <p>Student: <EMAIL></p>
                  <p>Teacher: <EMAIL></p>
                  <p>Admin: <EMAIL></p>
                  <p className="text-muted-foreground">Password: any 3+ characters</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
