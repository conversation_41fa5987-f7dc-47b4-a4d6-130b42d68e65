import { createFileRoute, redirect, useNavigate } from '@tanstack/react-router'
import React from 'react'
import { useAuth } from '@/contexts/auth-context'
import { LoginForm } from '@/components/auth/login-form'

export const Route = createFileRoute('/auth')({
  beforeLoad: ({ context, location }) => {
    // Redirect to dashboard if already authenticated
    const storedUser = localStorage.getItem('fme-user')
    if (storedUser) {
      throw redirect({
        to: '/dashboard',
      })
    }
  },
  component: AuthPage,
})

function AuthPage() {
  const { login } = useAuth()
  const navigate = useNavigate()

  const handleLogin = async (data: { email: string; password: string }) => {
    try {
      await login(data.email, data.password)
      // Redirect to dashboard on successful login
      navigate({ to: '/dashboard' })
    } catch (error) {
      // Error handling is done in the LoginForm component
      throw error
    }
  }

  const handleSwitchToRegister = () => {
    // For now, just show an alert. In the future, this could navigate to a register page
    alert('Registration feature coming soon!')
  }

  return (
    <div className="min-h-screen bg-white dark:bg-gray-950">
      {/* Header */}
      <header className="bg-brand-nav">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16 items-center">
            <div className="flex items-center">
              <h1 className="text-white font-bold text-xl">Foundation for Music Education</h1>
            </div>
            <a
              href="/"
              className="text-white hover:text-white/90 text-sm"
            >
              ← Back to Home
            </a>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div className="w-full max-w-md">
          {/* Background Card */}
          <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-8">
            <LoginForm
              onSubmit={handleLogin}
              onSwitchToRegister={handleSwitchToRegister}
            />

            <div className="mt-6 text-center">
              <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
                <p className="font-medium">Test Users:</p>
                <div className="text-xs space-y-1">
                  <p>Student: <EMAIL></p>
                  <p>Teacher: <EMAIL></p>
                  <p>Admin: <EMAIL></p>
                  <p className="text-muted-foreground">Password: any 3+ characters</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
