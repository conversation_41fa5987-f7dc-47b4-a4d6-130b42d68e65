import React, { useState } from 'react'
import { useForm } from '@tanstack/react-form'
import { cn } from '@/lib/utils'

interface LoginFormData {
  email: string
  password: string
}

interface LoginFormProps {
  onSubmit: (data: LoginFormData) => Promise<void>
  onSwitchToRegister: () => void
}

export function LoginForm({ onSubmit, onSwitchToRegister }: LoginFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const form = useForm<LoginFormData>({
    defaultValues: {
      email: '',
      password: '',
    },
    onSubmit: async ({ value }) => {
      setIsLoading(true)
      setError(null)
      try {
        await onSubmit(value)
      } catch (error) {
        console.error('Login failed:', error)
        setError(error instanceof Error ? error.message : 'Login failed')
      } finally {
        setIsLoading(false)
      }
    },
  })

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
          Welcome Back
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Sign in to your account
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}

      <form
        onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}
        className="space-y-6"
      >
        {/* Email Field */}
        <form.Field
          name="email"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Email is required'
              const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
              if (!emailRegex.test(value)) return 'Please enter a valid email'
              return undefined
            },
          }}
        >
          {(field) => (
            <div>
              <label 
                htmlFor={field.name} 
                className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"
              >
                Email Address
              </label>
              <input
                id={field.name}
                name={field.name}
                type="email"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                className={cn(
                  'w-full px-4 py-3 bg-white dark:bg-gray-800 border rounded-md text-gray-900 dark:text-gray-100',
                  'placeholder:text-gray-500 dark:placeholder:text-gray-400',
                  'focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2',
                  'transition-colors',
                  field.state.meta.errors.length > 0
                    ? 'border-red-300 dark:border-red-600'
                    : 'border-gray-300 dark:border-gray-600'
                )}
                placeholder="Enter your email"
                disabled={isLoading}
              />
              {field.state.meta.errors.map((error) => (
                <p key={error} className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {error}
                </p>
              ))}
            </div>
          )}
        </form.Field>

        {/* Password Field */}
        <form.Field
          name="password"
          validators={{
            onChange: ({ value }) => {
              if (!value) return 'Password is required'
              if (value.length < 6) return 'Password must be at least 6 characters'
              return undefined
            },
          }}
        >
          {(field) => (
            <div>
              <label 
                htmlFor={field.name} 
                className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2"
              >
                Password
              </label>
              <input
                id={field.name}
                name={field.name}
                type="password"
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                className={cn(
                  'w-full px-4 py-3 bg-white dark:bg-gray-800 border rounded-md text-gray-900 dark:text-gray-100',
                  'placeholder:text-gray-500 dark:placeholder:text-gray-400',
                  'focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2',
                  'transition-colors',
                  field.state.meta.errors.length > 0
                    ? 'border-red-300 dark:border-red-600'
                    : 'border-gray-300 dark:border-gray-600'
                )}
                placeholder="Enter your password"
                disabled={isLoading}
              />
              {field.state.meta.errors.map((error) => (
                <p key={error} className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {error}
                </p>
              ))}
            </div>
          )}
        </form.Field>

        {/* Forgot Password Link */}
        <div className="text-right">
          <button
            type="button"
            className="text-sm text-brand-button hover:text-brand-button-hover transition-colors"
          >
            Forgot your password?
          </button>
        </div>

        {/* Submit Button */}
        <form.Subscribe
          selector={(state) => [state.canSubmit, state.isSubmitting]}
        >
          {([canSubmit, isSubmitting]) => (
            <button
              type="submit"
              disabled={!canSubmit || isLoading}
              className={cn(
                'w-full bg-brand-button hover:bg-brand-button-hover text-white font-medium py-3 px-4 rounded-md',
                'transition-colors focus:outline-none focus:ring-2 focus:ring-brand-button focus:ring-offset-2',
                'disabled:opacity-50 disabled:cursor-not-allowed'
              )}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Signing In...
                </div>
              ) : (
                'Sign In'
              )}
            </button>
          )}
        </form.Subscribe>
      </form>

      {/* Switch to Register */}
      <div className="mt-6 text-center">
        <p className="text-gray-600 dark:text-gray-400">
          Don't have an account?{' '}
          <button
            type="button"
            onClick={onSwitchToRegister}
            className="text-brand-button hover:text-brand-button-hover font-medium transition-colors"
          >
            Sign up
          </button>
        </p>
      </div>
    </div>
  )
}
