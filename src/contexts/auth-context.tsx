import React, { createContext, useContext, useState, useEffect } from 'react'

export interface User {
  id: string
  email: string
  name: string
  role: 'student' | 'teacher' | 'admin'
}

interface AuthContextValue {
  user: User | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined)

// Test users for development
const TEST_USERS: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'Test Student',
    role: 'student'
  },
  {
    id: '2', 
    email: '<EMAIL>',
    name: 'Test Teacher',
    role: 'teacher'
  },
  {
    id: '3',
    email: '<EMAIL>', 
    name: 'Test Admin',
    role: 'admin'
  }
]

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check for stored user on mount (only on client side)
    if (typeof window !== 'undefined') {
      const storedUser = localStorage.getItem('fme-user')
      if (storedUser) {
        try {
          setUser(JSON.parse(storedUser))
        } catch (error) {
          console.error('Error parsing stored user:', error)
          localStorage.removeItem('fme-user')
        }
      }
    }
    setIsLoading(false)
  }, [])

  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true)
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Find test user
    const testUser = TEST_USERS.find(u => u.email === email)
    
    if (!testUser) {
      setIsLoading(false)
      throw new Error('Invalid email or password')
    }
    
    // For demo purposes, any password works for test users
    if (password.length < 3) {
      setIsLoading(false)
      throw new Error('Password must be at least 3 characters')
    }
    
    // Store user (only on client side)
    if (typeof window !== 'undefined') {
      localStorage.setItem('fme-user', JSON.stringify(testUser))
    }
    setUser(testUser)
    setIsLoading(false)
  }

  const logout = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('fme-user')
    }
    setUser(null)
  }

  const value: AuthContextValue = {
    user,
    isLoading,
    login,
    logout,
    isAuthenticated: !!user
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
