@import "tailwindcss";

/* Base styles */
body {
  @apply m-0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

/* Theme Variables */
@layer theme {
  :root {
    --color-background: 0 0% 100%;
    --color-foreground: 222.2 84% 4.9%;
    --color-card: 0 0% 100%;
    --color-card-foreground: 222.2 84% 4.9%;
    --color-popover: 0 0% 100%;
    --color-popover-foreground: 222.2 84% 4.9%;
    
    /* Primary colors based on your brand button color (#008544) */
    --color-primary: 145 100% 26%;  /* #008544 converted to HSL */
    --color-primary-foreground: 0 0% 100%;
    
    /* Secondary colors based on your nav color (#032741) */
    --color-secondary: 202 88% 13%;  /* #032741 converted to HSL */
    --color-secondary-foreground: 0 0% 100%;
    
    --color-muted: 210 40% 96%;
    --color-muted-foreground: 215.4 16.3% 46.9%;
    --color-accent: 210 40% 96%;
    --color-accent-foreground: 222.2 84% 4.9%;
    --color-destructive: 0 84.2% 60.2%;
    --color-destructive-foreground: 210 40% 98%;
    --color-border: 214.3 31.8% 91.4%;
    --color-input: 214.3 31.8% 91.4%;
    --color-ring: 145 100% 26%; /* Same as primary for consistency */
    --radius: 0.5rem;
    
    /* Brand-specific variables */
    --color-brand-nav: 202 88% 13%;
    --color-brand-nav-hover: 202 88% 20%;
    --color-brand-button: 145 100% 26%;
    --color-brand-button-hover: 145 100% 32%;
  }
 
  .dark {
    --color-background: 222.2 84% 4.9%;
    --color-foreground: 210 40% 98%;
    --color-card: 222.2 84% 4.9%;
    --color-card-foreground: 210 40% 98%;
    --color-popover: 222.2 84% 4.9%;
    --color-popover-foreground: 210 40% 98%;
    
    /* Primary - slightly lighter for better contrast in dark mode */
    --color-primary: 145 100% 35%;  /* Lighter version of #008544 */
    --color-primary-foreground: 222.2 84% 4.9%;
    
    /* Secondary - lighter nav color for dark mode */
    --color-secondary: 202 88% 25%;  /* Lighter version of #032741 */
    --color-secondary-foreground: 210 40% 98%;
    
    --color-muted: 217.2 32.6% 17.5%;
    --color-muted-foreground: 215 20.2% 65.1%;
    --color-accent: 217.2 32.6% 17.5%;
    --color-accent-foreground: 210 40% 98%;
    --color-destructive: 0 62.8% 30.6%;
    --color-destructive-foreground: 210 40% 98%;
    --color-border: 217.2 32.6% 17.5%;
    --color-input: 217.2 32.6% 17.5%;
    --color-ring: 145 100% 35%;
    
    /* Brand-specific dark mode variables */
    --color-brand-nav: 202 88% 25%;
    --color-brand-nav-hover: 202 88% 35%;
    --color-brand-button: 145 100% 35%;
    --color-brand-button-hover: 145 100% 42%;
  }
}

/* Brand utility classes */
@layer utilities {
  .bg-brand-nav {
    background-color: hsl(var(--color-brand-nav));
  }
  
  .bg-brand-nav-hover {
    background-color: hsl(var(--color-brand-nav-hover));
  }
  
  .bg-brand-button {
    background-color: hsl(var(--color-brand-button));
  }
  
  .bg-brand-button-hover {
    background-color: hsl(var(--color-brand-button-hover));
  }
  
  .text-brand-nav {
    color: hsl(var(--color-brand-nav));
  }
  
  .text-brand-button {
    color: hsl(var(--color-brand-button));
  }
  
  .border-brand-nav {
    border-color: hsl(var(--color-brand-nav));
  }
  
  .border-brand-button {
    border-color: hsl(var(--color-brand-button));
  }
}

/* Base component styles */
@layer base {
  body {
    @apply bg-white text-gray-950;
  }
  
  .dark body {
    @apply bg-gray-950 text-gray-50;
  }
}

/* Enhanced focus styles for accessibility */
@layer utilities {
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2;
  }
  
  .focus-visible-ring-brand {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
    --tw-ring-color: hsl(var(--color-brand-button));
  }
}

/* Smooth transitions for theme switching */
@layer base {
  html {
    transition: color-scheme 0.2s ease-in-out;
  }
  
  * {
    transition: background-color 0.2s ease-in-out, 
                border-color 0.2s ease-in-out,
                color 0.2s ease-in-out;
  }
}
